export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      question: {
        Row: {
          answerChoices: string[] | null
          correctAnswer: string
          createdAt: string
          difficulty: Database["public"]["Enums"]["Difficulty"]
          explanation: string
          id: number
          intro: string | null
          isSimulationQuestion: boolean
          model: string | null
          passage: string | null
          passage2: string | null
          question: string
          questionType: Database["public"]["Enums"]["Question Type"]
          topic: Database["public"]["Enums"]["Topic"]
        }
        Insert: {
          answerChoices?: string[] | null
          correctAnswer: string
          createdAt?: string
          difficulty: Database["public"]["Enums"]["Difficulty"]
          explanation: string
          id?: number
          intro?: string | null
          isSimulationQuestion?: boolean
          model?: string | null
          passage?: string | null
          passage2?: string | null
          question: string
          questionType: Database["public"]["Enums"]["Question Type"]
          topic: Database["public"]["Enums"]["Topic"]
        }
        Update: {
          answerChoices?: string[] | null
          correctAnswer?: string
          createdAt?: string
          difficulty?: Database["public"]["Enums"]["Difficulty"]
          explanation?: string
          id?: number
          intro?: string | null
          isSimulationQuestion?: boolean
          model?: string | null
          passage?: string | null
          passage2?: string | null
          question?: string
          questionType?: Database["public"]["Enums"]["Question Type"]
          topic?: Database["public"]["Enums"]["Topic"]
        }
        Relationships: []
      }
      simulations: {
        Row: {
          createdAt: string
          M1: number[]
          M2: number[]
          RW1: number[]
          RW2: number[]
          slug: number
          title: string
        }
        Insert: {
          createdAt?: string
          M1: number[]
          M2: number[]
          RW1: number[]
          RW2: number[]
          slug?: number
          title: string
        }
        Update: {
          createdAt?: string
          M1?: number[]
          M2?: number[]
          RW1?: number[]
          RW2?: number[]
          slug?: number
          title?: string
        }
        Relationships: []
      }
      vocabCards: {
        Row: {
          charge: Database["public"]["Enums"]["vocabCharge"] | null
          definition: string
          difficulty: number | null
          hint1: string | null
          hint2: string | null
          id: number
          partOfSpeech: string | null
          word: string
        }
        Insert: {
          charge?: Database["public"]["Enums"]["vocabCharge"] | null
          definition: string
          difficulty?: number | null
          hint1?: string | null
          hint2?: string | null
          id?: number
          partOfSpeech?: string | null
          word: string
        }
        Update: {
          charge?: Database["public"]["Enums"]["vocabCharge"] | null
          definition?: string
          difficulty?: number | null
          hint1?: string | null
          hint2?: string | null
          id?: number
          partOfSpeech?: string | null
          word?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      Difficulty: "Easy" | "Medium" | "Hard"
      "Question Type":
        | "Word in Context"
        | "Main Purpose Underlined"
        | "Main Idea"
        | "Main Purpose"
        | "Overall Structure"
        | "Specific Detail"
        | "Command of Evidence"
        | "Paired Passage"
        | "Inference"
        | "Student Notes"
        | "Transitions"
        | "Punctuation"
        | "Grammar"
        | "Inequalities"
        | "Equations"
        | "Graphs"
        | "Other Functions"
        | "Statistics"
        | "Numerical Relations"
        | "Probability"
        | "Data Modelling"
        | "Lines, Angles and Triangles"
        | "2D & 3D Shapes"
        | "Trigonometry"
        | "Coordinate Plane"
        | "Circles"
      Topic:
        | "Social Science"
        | "Natural Science"
        | "Humanities"
        | "Fiction"
        | "Algebra"
        | "Data Analysis"
        | "Geometry"
      vocabCharge: "Positive" | "Negative" | "Neutral"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      Difficulty: ["Easy", "Medium", "Hard"],
      "Question Type": [
        "Word in Context",
        "Main Purpose Underlined",
        "Main Idea",
        "Main Purpose",
        "Overall Structure",
        "Specific Detail",
        "Command of Evidence",
        "Paired Passage",
        "Inference",
        "Student Notes",
        "Transitions",
        "Punctuation",
        "Grammar",
        "Inequalities",
        "Equations",
        "Graphs",
        "Other Functions",
        "Statistics",
        "Numerical Relations",
        "Probability",
        "Data Modelling",
        "Lines, Angles and Triangles",
        "2D & 3D Shapes",
        "Trigonometry",
        "Coordinate Plane",
        "Circles",
      ],
      Topic: [
        "Social Science",
        "Natural Science",
        "Humanities",
        "Fiction",
        "Algebra",
        "Data Analysis",
        "Geometry",
      ],
      vocabCharge: ["Positive", "Negative", "Neutral"],
    },
  },
} as const
