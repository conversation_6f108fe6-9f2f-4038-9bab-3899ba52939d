<script lang="ts">
    import { <PERSON>, Axis, Spline, Svg, Highlight, Tooltip, <PERSON><PERSON><PERSON>, Points } from 'layerchart';
    import { scaleBand, scaleLinear, scalePoint } from 'd3-scale';
    import { onSnapshot, doc } from 'firebase/firestore';
    import { browser } from '$app/environment';

    import H4 from '$lib/ui/typography/H4.svelte';
    import P1 from '$lib/ui/typography/P1.svelte';
    import { db } from '$lib/firebase/firestore';
    import { user } from '$lib/firebase/auth.svelte';
    import type { CompletedQuestion } from '$lib/types/question.types';
	import { onDestroy } from 'svelte';

    // Loading and error states
    let isLoading = $state(true);
    let error = $state<string | null>(null);

    // Firebase data
    let completedQuestionsData = $state<CompletedQuestion | null>(null);

    // Process Firebase data into chart format
    interface DayData {
        day: string;
        currentPeriod: number;  // This week (Sunday to Saturday)
        previousPeriod: number; // Last week (Sunday to Saturday)
        accuracy: number;       // Accuracy for current period
        date: string;           // For tracking actual dates
    }

    // Helper function to get day abbreviation from date
    function getDayAbbreviation(date: Date): string {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[date.getDay()];
    }

    // Helper function to format date as YYYY-MM-DD
    function formatDate(date: Date): string {
        return date.toISOString().split('T')[0];
    }

    // Helper function to safely create a date from timestamp
    function createSafeDate(timestamp: string): Date {
        const date = new Date(timestamp);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            return new Date(); // Return current date as fallback
        }
        return date;
    }

    // Helper function to get the start of the week (Sunday)
    function getWeekStart(date: Date): Date {
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay()); // Sunday is day 0
        weekStart.setHours(0, 0, 0, 0); // Start of day
        return weekStart;
    }

    // Helper function to get the end of the week (Saturday)
    function getWeekEnd(date: Date): Date {
        const weekEnd = new Date(date);
        weekEnd.setDate(date.getDate() - date.getDay() + 6); // Saturday is day 6
        weekEnd.setHours(23, 59, 59, 999); // End of day
        return weekEnd;
    }

    // Helper function to get this week's days (Sunday to Saturday)
    function getThisWeekDays(): Date[] {
        const days = [];
        const today = new Date();
        const weekStart = getWeekStart(today);

        // Ensure we have a valid date
        if (isNaN(today.getTime())) {
            console.error('Invalid current date, using fallback');
            const fallbackDate = new Date('2024-01-01');
            const fallbackWeekStart = getWeekStart(fallbackDate);
            for (let i = 0; i < 7; i++) {
                const date = new Date(fallbackWeekStart);
                date.setDate(fallbackWeekStart.getDate() + i);
                days.push(date);
            }
            return days;
        }

        for (let i = 0; i < 7; i++) {
            const date = new Date(weekStart);
            date.setDate(weekStart.getDate() + i);
            days.push(date);
        }
        return days;
    }

    // Process completed questions data into this week and last week chart data
    function processQuestionData(data: CompletedQuestion | null): DayData[] {
        const thisWeekDays = getThisWeekDays();

        if (!data?.data || !Array.isArray(data.data)) {
            return thisWeekDays.map((date: Date) => ({
                day: getDayAbbreviation(date),
                currentPeriod: 0,
                previousPeriod: 0,
                accuracy: 0,
                date: formatDate(date)
            }));
        }

        // Get date ranges for both weeks
        const today = new Date();
        const thisWeekStart = getWeekStart(today);
        const thisWeekEnd = getWeekEnd(today);

        // Last week is the week before this week
        const lastWeekEnd = new Date(thisWeekStart);
        lastWeekEnd.setDate(thisWeekStart.getDate() - 1); // Day before this week starts
        const lastWeekStart = getWeekStart(lastWeekEnd);

        // Initialize data structure for both weeks
        const dailyData: Record<string, {
            currentPeriod: number;
            previousPeriod: number;
            correct: number;
            total: number
        }> = {};

        thisWeekDays.forEach((date: Date) => {
            const dateStr = getDayAbbreviation(date);
            dailyData[dateStr] = { currentPeriod: 0, previousPeriod: 0, correct: 0, total: 0 };
        });

        // Process each completed question
        data.data.forEach(item => {
            // Skip items with invalid or missing timestamps
            if (!item.timestamp) {
                return;
            }

            const questionDate = createSafeDate(item.timestamp);
            const dayOfWeek = getDayAbbreviation(questionDate);

            // Check if question is from this week (current period)
            if (questionDate >= thisWeekStart && questionDate <= today) {
                dailyData[dayOfWeek].currentPeriod++;
                dailyData[dayOfWeek].total++;
                if (item.wasAnswerCorrect) {
                    dailyData[dayOfWeek].correct++;
                }
            }
            // Check if question is from last week (previous period)
            else if (questionDate >= lastWeekStart && questionDate <= lastWeekEnd) {
                dailyData[dayOfWeek].previousPeriod++;
            }
        });

        // Convert to chart format with accuracy calculation
        return thisWeekDays.map((date: Date) => {
            const dayOfWeek = getDayAbbreviation(date);
            const dayData = dailyData[dayOfWeek];
            return {
                day: dayOfWeek,
                currentPeriod: dayData.currentPeriod,
                previousPeriod: dayData.previousPeriod,
                accuracy: dayData.total > 0
                    ? Math.round((dayData.correct / dayData.total) * 100)
                    : 0,
                date: formatDate(date)
            };
        });
    }

    // Helper function to calculate overall accuracy for the current period
    function calculateOverallAccuracy(data: DayData[]): number {
        const totalQuestions = data.reduce((sum, d) => sum + d.currentPeriod, 0);
        if (totalQuestions === 0) return 0;

        const weightedAccuracy = data.reduce((sum, d) => sum + (d.accuracy * d.currentPeriod), 0);
        return Math.round(weightedAccuracy / totalQuestions);
    }



    // Helper function to calculate percentage change
    function calculatePercentageChange(current: number, previous: number): { value: number; isPositive: boolean; isZero: boolean } {
        if (previous === 0) {
            return { value: current > 0 ? 100 : 0, isPositive: current > 0, isZero: current === 0 };
        }
        const change = ((current - previous) / previous) * 100;
        return {
            value: Math.abs(Math.round(change)),
            isPositive: change > 0,
            isZero: Math.abs(change) < 1
        };
    }

    // Derived values for display
    let dsatData = $derived(processQuestionData(completedQuestionsData));
    let currentPeriodTotal = $derived(dsatData.reduce((sum, d) => sum + d.currentPeriod, 0));
    let previousPeriodTotal = $derived(dsatData.reduce((sum, d) => sum + d.previousPeriod, 0));
    let overallAccuracy = $derived(calculateOverallAccuracy(dsatData));

    // Calculate percentage changes between current and previous periods
    let questionCountChange = $derived(calculatePercentageChange(currentPeriodTotal, previousPeriodTotal));

    // Firebase subscription
    let unsubscribe: (() => void) | null = null;

    $effect(() => {
        if (browser && $user?.uid) {
            isLoading = true;
            error = null;

            const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', 'dataDoc');

            unsubscribe = onSnapshot(
                completedQuestionsRef,
                (snapshot) => {
                    try {
                        if (snapshot.exists()) {
                            completedQuestionsData = snapshot.data() as CompletedQuestion;
                        } else {
                            completedQuestionsData = null;
                        }
                        isLoading = false;
                    } catch (err) {
                        console.error('Error processing question bank data:', err);
                        error = 'Failed to process progress data';
                        isLoading = false;
                    }
                },
                (err) => {
                    console.error('Error fetching question bank progress:', err);
                    error = 'Failed to load progress data';
                    isLoading = false;
                }
            );
        } else if (!$user) {
            // User not logged in
            completedQuestionsData = null;
            isLoading = false;
        }
    });

    onDestroy(() => {
        if (unsubscribe) {
            unsubscribe();
            unsubscribe = null;
        }
    });
</script>


<H4>Question Bank Progress</H4>

{#if isLoading}
    <div class="loading-container">
        <img src="/loading.gif" alt="Loading..." width="128" height="128">
    </div>
{:else if error}
    <div class="error-container">
        <P1>⚠️ {error}</P1>
        <P1>Please try refreshing the page.</P1>
    </div>
{:else if currentPeriodTotal === 0 && previousPeriodTotal === 0}
    <div class="no-data-container">
        <a href="/study/question-bank" class="no-data-link">
            <div class="no-data-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 10C21.1313 10 21.2614 9.97419 21.3827 9.92395C21.5041 9.8737 21.6143 9.80005 21.7072 9.70718C21.8 9.61432 21.8737 9.50406 21.9239 9.38272C21.9742 9.26138 22 9.13133 22 9V6C22.0001 5.79017 21.9341 5.58565 21.8114 5.41544C21.6887 5.24524 21.5155 5.11799 21.3164 5.05176L12.3164 2.05176C12.111 1.9834 11.889 1.9834 11.6836 2.05176L2.68359 5.05176C2.48449 5.11799 2.31131 5.24524 2.18861 5.41544C2.0659 5.58565 1.99991 5.79017 2 6V9C1.99997 9.13133 2.02581 9.26138 2.07605 9.38272C2.12629 9.50406 2.19995 9.61432 2.29282 9.70718C2.38568 9.80005 2.49594 9.8737 2.61728 9.92395C2.73862 9.97419 2.86867 10 3 10H4V17.1843C3.41674 17.3897 2.91137 17.7707 2.55327 18.2748C2.19517 18.779 2.0019 19.3816 2 20V22C1.99997 22.1313 2.02581 22.2614 2.07605 22.3827C2.12629 22.5041 2.19995 22.6143 2.29282 22.7072C2.38568 22.8 2.49594 22.8737 2.61728 22.9239C2.73862 22.9742 2.86867 23 3 23H21C21.1313 23 21.2614 22.9742 21.3827 22.9239C21.5041 22.8737 21.6143 22.8 21.7072 22.7072C21.8 22.6143 21.8737 22.5041 21.9239 22.3827C21.9742 22.2614 22 22.1313 22 22V20C21.9981 19.3816 21.8048 18.779 21.4467 18.2748C21.0886 17.7707 20.5833 17.3897 20 17.1843V10H21ZM20 21H4V20C4.00026 19.7349 4.10571 19.4807 4.29319 19.2932C4.48066 19.1057 4.73486 19.0003 5 19H19C19.2651 19.0003 19.5193 19.1057 19.7068 19.2932C19.8943 19.4807 19.9997 19.7349 20 20V21ZM6 17V10H8V17H6ZM10 17V10H14V17H10ZM16 17V10H18V17H16ZM4 8V6.7207L12 4.0537L20 6.7207V8H4Z" fill="currentColor"/>
                </svg>
            </div>
            <H4>No Progress Data Yet</H4>
            <P1>Start using the Question Bank to see your progress and accuracy statistics here.</P1>
            <div class="no-data-subtitle">
                <P1>Your daily progress and performance trends will appear once you begin answering questions.</P1>
            </div>
        </a>
    </div>
{:else}
    <div class="stat-container flex flex-col w-full">
        <div class="flex justify-between flex-row w-full" style="color: var(--sky-blue)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-this-week"></div>
                <P1 isBold>This Week</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{currentPeriodTotal} questions</P1>
                {#if previousPeriodTotal > 0 && !questionCountChange.isZero}
                    <span class="change-indicator" class:positive={questionCountChange.isPositive} class:negative={!questionCountChange.isPositive}>
                        {questionCountChange.isPositive ? '↗' : '↘'} {questionCountChange.value}%
                    </span>
                {/if}
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: var(--aquamarine)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-accuracy"></div>
                <P1 isBold>Acc. rate</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{overallAccuracy}%</P1>
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: #A7A7A7;">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-last-week"></div>
                <P1 isBold>Last Week</P1>
            </div>
            <P1 isBold>{previousPeriodTotal} questions</P1>
        </div>
    </div>
{/if}

{#if !isLoading && !error && $user && (currentPeriodTotal > 0 || previousPeriodTotal > 0)}
    <div class="dsat-chart h-[300px] grid p-4">
        <div class="col-start-1 row-start-1 z-0">
            <BarChart
                data={dsatData}
                x="day"
                xScale={scaleBand().domain(dsatData.map(d => d.day)).padding(0.5)}
                series={[
                    { key: "currentPeriod", color: "var(--sky-blue)" },
                    { key: "previousPeriod", color: "#A7A7A7" }
                ]}
                seriesLayout="group"
                props={{
                    xAxis: { format: "none" },
                    yAxis: { format: "metric" },
                    tooltip: { header: { format: "none" } },
                }}
                padding={{ left: 16, bottom: 24 }}
            />
        </div>

        <div class="col-start-1 row-start-1 z-10">
            <Chart
                data={dsatData}
                x="day"
                xScale={scalePoint().domain(dsatData.map(d => d.day)).padding(0.75)}
                y="accuracy"
                yDomain={[0, 100]}
                padding={{ left: 16, bottom: 24 }}
                tooltip={{ mode: "band" }}
                let:height
            >
                <Svg>
                    <Axis
                        placement="right"
                        scale={scaleLinear([0, 100], [height, 0])}
                        format={(v: number) => `${v}%`}
                    />
                    <Spline class="stroke-2" style="stroke: var(--aquamarine)" />
                    <Points class="fill-primary stroke-primary" />
                    <Highlight points lines />
                </Svg>

                <Tooltip.Root let:data classes={{ root: "bg-white rounded-md border border-black shadow-[0.125rem_0.125rem_0_#000000]" }}>
                    <Tooltip.Header>{data.day}</Tooltip.Header>
                    <Tooltip.List>
                        <Tooltip.Item label="This Week" value={data.currentPeriod} format="metric" />
                        <Tooltip.Item label="Last Week" value={data.previousPeriod} format="metric" />
                        <Tooltip.Item label="Accuracy" value={`${data.accuracy}%`} />
                    </Tooltip.List>
                </Tooltip.Root>
            </Chart>
        </div>
    </div>
{/if}

  


<style>
    * {
        --color-primary: 157, 79%, 63%; /* aquamarine in HSL */
        --color-secondary: 100, 50%, 50%;
    }
    
    .dsat-chart {
        width: 100%;
    }

    .legend-this-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: var(--sky-blue, #66E2FF);
    }

    .legend-accuracy {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        background: var(--aquamarine);
        border-radius: 5px;
    }

    .legend-last-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: #A7A7A7;
    }

    .loading-container,
    .error-container,
    .no-data-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        min-height: 200px;
    }

    .error-container {
        color: var(--rose, #EB47AB);
    }

    .no-data-container {
        gap: 1rem;
    }

    .no-data-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: inherit;
        gap: 1rem;
        padding: 1.5rem;
        border-radius: 0.75rem;
        border: 2px dashed #ccc;
        transition: all 0.2s ease;
        max-width: 500px;
    }

    .no-data-link:hover {
        border-color: var(--rose);
        background-color: rgba(235, 71, 171, 0.05);
        transform: translateY(-2px);
    }

    .no-data-icon {
        color: var(--rose);
        margin-bottom: 0.5rem;
        transition: transform 0.2s ease;
    }

    .no-data-link:hover .no-data-icon {
        transform: scale(1.1);
    }

    .no-data-subtitle {
        opacity: 0.7;
        max-width: 400px;
    }

    .change-indicator {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        border: 1px solid;
        background: var(--white);
        white-space: nowrap;
    }

    .change-indicator.positive {
        color: var(--aquamarine);
        border-color: var(--aquamarine);
        background: var(--light-aquamarine);
    }

    .change-indicator.negative {
        color: var(--rose);
        border-color: var(--rose);
        background: var(--light-rose);
    }
</style>