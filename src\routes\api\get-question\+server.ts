import { dev } from "$app/environment";
import { supabase, adminAuth } from "$lib/server";
import { error, json } from "@sveltejs/kit";
import { Ratelimit } from "@upstash/ratelimit";
import type { PostgrestError } from "@supabase/supabase-js";
import type { Question } from "$lib/types";
import { redis } from "$lib/server/redis.ts";

const ratelimit = {
    free: new Ratelimit({
        redis: redis,
        limiter: Ratelimit.fixedWindow(7, "1 d"),
    }),
    paid: new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(60, "1 h"),
    })
};

interface RequestPayload {
    chosenDifficulties: string[];
    chosenTypes: string[];
    questionIds?: number[];
    excludedQuestionIds?: number[];
}



export const POST = async ({ request }) => {
    const authHeader = request.headers.get('authorization');
    let identifier: string;
    let rateLimitType: 'free' | 'paid';

    const { chosenDifficulties, chosenTypes, questionIds, excludedQuestionIds } = await request.json();

    // * Check if the user is authenticated
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Use IP address as identifier for users without bearer token
        const ip = request.headers.get('x-real-ip') || request.headers.get('x-forwarded-for') || 'unknown';
        // Make IP less specific by using only first three octets
        identifier = ip.split('.').slice(0, 3).join('.') || 'unknown';
        rateLimitType = 'free';
    } else {
        const idToken = authHeader.split(' ')[1];
        try {
            const decodedToken = await adminAuth.verifyIdToken(idToken);
            identifier = decodedToken.uid;
            const role = (await adminAuth.getUser(identifier)).customClaims.role;

            // Check for questionBank custom claim
            const hasQuestionBankAccess = role === 'Question Bank' || role === 'Pro';
            rateLimitType = hasQuestionBankAccess ? 'paid' : 'free';
        } catch (err) {
            // If token is invalid, treat as free user
            const ip = request.headers.get('x-real-ip') || request.headers.get('x-forwarded-for') || 'unknown';
            // Make IP less specific by using only first three octets
            identifier = ip.split('.').slice(0, 3).join('.') || 'unknown';
            rateLimitType = 'free';
        }
    }

    rateLimitType = 'paid';

    const { success } = await ratelimit[rateLimitType].limit(identifier + ":getQuestion");
    if (!success) {
        return error(429, 'Rate limit exceeded');
    }

    // Difficulty and question type are required
    if (chosenDifficulties.length === 0 || chosenTypes.length === 0) {
        return error(400, 'Difficulty and question type are required');
    }

    // Supabase query for questions
    let query = supabase
        .from('random_question')
        .select('id, passage, passage2, question, correctAnswer, answerChoices, explanation, topic, difficulty, questionType')

    // Filter by difficulty, type, and topic
    if (chosenDifficulties.length > 0) query = query.in('difficulty', chosenDifficulties);
    if (chosenTypes.length > 0) query = query.in('questionType', chosenTypes);

    // Apply filters based on the filter index
    if (questionIds?.length > 0) query = query.in('id', questionIds);
    if (excludedQuestionIds?.length > 0) query = query.not('id', 'in', `(${excludedQuestionIds.join(',')})`);

    // Filter out simulation questions
    query = query.eq('isSimulationQuestion', false);

    // Fetch a random question
    // @ts-ignore
    query = query.limit(1).maybeSingle();

    // Fetch all matching questions (for random selection)
    // @ts-ignore
    const { data: question, error: qError }: { data: Question, error: PostgrestError } = await query;
    
    if (qError) {
        return error(500, qError.message);
    }
    if (!question) {
        return json({ message: 'No questions found. It seems you\'ve completed all questions for this filter!' });
    }

    // Remove sensitive fields if present
    delete question.model;

    // Change correctAnswer to number for MCQ
    if (question.answerChoices) question.correctAnswer = Number(question.correctAnswer) as 0 | 1 | 2 | 3;

    const origin = request.headers.get('origin');
    const allowedOrigins = ['https://www.dsat16.com', 'https://blog.dsat16.com'];
    if (dev) allowedOrigins.push('http://localhost:5173');

    return json({ question }, {
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'https://www.dsat16.com',
        }
    });
}

export const OPTIONS = async ({ request }) => {
    if (dev) {
        return new Response(null, { status: 204 });
    }
    
    const origin = request.headers.get('origin');
    const allowedOrigins = ['https://www.dsat16.com', 'https://blog.dsat16.com'];
    if (dev) allowedOrigins.push('http://localhost:5173');
    
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'https://www.dsat16.com',
            'Access-Control-Allow-Methods': 'POST, OPTIONS', 
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
};